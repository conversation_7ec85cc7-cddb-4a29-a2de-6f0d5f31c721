import React, { useState } from 'react';
import { Form, Tabs } from 'antd';
import { SettingOutlined, AlertOutlined, DatabaseOutlined, NotificationOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';

// 导入重构后的模块
import type { TaskBasic } from '@/types/task';
import type { TaskAlert } from '../../types/task';
import { formStyles } from '@/components/SqlMonitor/styles';
import { DbConnectionModal, AlertSendModal } from '@/components/SqlMonitor/components/modals/TaskFormModals';
import { AlertDrawer } from '../drawers/AlertDrawer';
import { OtherInfoModal, SelectModal } from '../modals/TaskFormModalsExtended';

// 导入重构后的子组件
import BasicInfoForm from './BasicInfoForm';
import AlertConfigTab from '../tabs/AlertConfigTab';
import DatabaseConfigTab from '../tabs/DatabaseConfigTab';
import NotificationConfigTab from '../tabs/NotificationConfigTab';
import OtherInfoConfigTab from '../tabs/OtherInfoConfigTab';

// 导入自定义 hooks
import { useFormData, useModalStates, useAvailableData } from '../../hooks';

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
}

/**
 * 复合任务表单组件
 * 支持多标签页展示不同类型的配置
 */
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({ initialData }) => {
  const [activeTab, setActiveTab] = useState('basic');

  // 使用自定义 hooks 管理状态和逻辑
  const { form, alerts, alertSends, dbConnection, otherInfo, setAlerts, setAlertSends, setDbConnection, setOtherInfo } = useFormData({ initialData });

  const { alertModal, dbConnectionModal, alertSendModal, otherInfoModal, selectModal, setAlertModal, setDbConnectionModal, setAlertSendModal, setOtherInfoModal, setSelectModal } = useModalStates();

  const { availableAlerts, availableDbConnections, availableAlertSends, availableOtherInfos } = useAvailableData();

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: (
        <span>
          <SettingOutlined />
          <span style={{ marginLeft: 8 }}>基本信息配置</span>
        </span>
      ),
      children: <BasicInfoForm form={form} />,
    },
    {
      key: 'alert',
      label: (
        <span>
          <AlertOutlined />
          <span style={{ marginLeft: 8 }}>监控项配置</span>
        </span>
      ),
      children: (
        <AlertConfigTab
          alerts={alerts}
          onAlertsChange={setAlerts}
          onAddAlert={() => setAlertModal({ visible: true, editingIndex: -1 })}
          onEditAlert={index => setAlertModal({ visible: true, editingIndex: index })}
          onSelectAlert={() => setSelectModal({ visible: true, type: 'alert', multiple: true })}
        />
      ),
    },
    {
      key: 'database',
      label: (
        <span>
          <DatabaseOutlined />
          <span style={{ marginLeft: 8 }}>数据库连接配置</span>
        </span>
      ),
      children: (
        <DatabaseConfigTab
          dbConnection={dbConnection}
          onDbConnectionChange={setDbConnection}
          onAddDbConnection={() => setDbConnectionModal({ visible: true, editingIndex: -1 })}
          onEditDbConnection={() => setDbConnectionModal({ visible: true, editingIndex: -1 })}
          onSelectDbConnection={() =>
            setSelectModal({
              visible: true,
              type: 'dbConnection',
              multiple: false,
            })
          }
        />
      ),
    },
    {
      key: 'notification',
      label: (
        <span>
          <NotificationOutlined />
          <span style={{ marginLeft: 8 }}>告警发送配置</span>
        </span>
      ),
      children: <NotificationConfigTab alertSends={alertSends} onAlertSendsChange={setAlertSends} />,
    },
    {
      key: 'other',
      label: (
        <span>
          <InfoCircleOutlined />
          <span style={{ marginLeft: 8 }}>附加信息配置</span>
        </span>
      ),
      children: (
        <OtherInfoConfigTab
          otherInfo={otherInfo}
          onOtherInfoChange={setOtherInfo}
          onAddOtherInfo={() => setOtherInfoModal({ visible: true, editingIndex: -1 })}
          onEditOtherInfo={() => setOtherInfoModal({ visible: true, editingIndex: -1 })}
          onSelectOtherInfo={() =>
            setSelectModal({
              visible: true,
              type: 'otherInfo',
              multiple: false,
            })
          }
        />
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <Form form={form} layout="vertical" className="flex-1 overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className={`${formStyles.formTabs} h-full`}
          style={{ height: '100%' }}
          tabBarStyle={{
            marginBottom: 0,
            paddingLeft: 16,
            paddingRight: 16,
            borderBottom: '1px solid #f0f0f0',
            background: '#fafafa',
          }}
        />
      </Form>

      {/* 告警抽屉 */}
      <AlertDrawer
        visible={alertModal.visible}
        editingData={alertModal.editingIndex !== undefined && alertModal.editingIndex >= 0 ? alerts[alertModal.editingIndex] : undefined}
        onCancel={() => setAlertModal({ visible: false, editingIndex: -1 })}
        onSubmit={(data: TaskAlert) => {
          if (alertModal.editingIndex !== undefined && alertModal.editingIndex >= 0) {
            // 编辑
            const newAlerts = [...alerts];
            newAlerts[alertModal.editingIndex] = data;
            setAlerts(newAlerts);
          } else {
            // 新增
            setAlerts([...alerts, data]);
          }
          setAlertModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 数据库连接Modal */}
      <DbConnectionModal
        visible={dbConnectionModal.visible}
        editingData={dbConnection || undefined}
        onCancel={() => setDbConnectionModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          setDbConnection(data);
          setDbConnectionModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 告警发送Modal */}
      <AlertSendModal
        visible={alertSendModal.visible}
        editingData={alertSendModal.editingIndex !== undefined && alertSendModal.editingIndex >= 0 ? alertSends[alertSendModal.editingIndex] : undefined}
        onCancel={() => setAlertSendModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          if (alertSendModal.editingIndex !== undefined && alertSendModal.editingIndex >= 0) {
            // 编辑
            const newAlertSends = [...alertSends];
            newAlertSends[alertSendModal.editingIndex] = data;
            setAlertSends(newAlertSends);
          } else {
            // 新增
            setAlertSends([...alertSends, data]);
          }
          setAlertSendModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 其他信息Modal */}
      <OtherInfoModal
        visible={otherInfoModal.visible}
        editingData={otherInfo || undefined}
        onCancel={() => setOtherInfoModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          setOtherInfo(data);
          setOtherInfoModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 选择Modal */}
      <SelectModal
        visible={selectModal.visible}
        type={selectModal.type}
        data={
          selectModal.type === 'alert'
            ? availableAlerts
            : selectModal.type === 'alertSend'
              ? availableAlertSends
              : selectModal.type === 'dbConnection'
                ? availableDbConnections
                : selectModal.type === 'otherInfo'
                  ? availableOtherInfos
                  : []
        }
        selectedData={
          selectModal.type === 'alert'
            ? alerts
            : selectModal.type === 'alertSend'
              ? alertSends
              : selectModal.type === 'dbConnection'
                ? dbConnection
                  ? [dbConnection]
                  : []
                : selectModal.type === 'otherInfo'
                  ? otherInfo
                    ? [otherInfo]
                    : []
                  : []
        }
        multiple={selectModal.multiple}
        onCancel={() => setSelectModal({ visible: false, type: 'alert', multiple: false })}
        onSubmit={selectedItems => {
          // 处理选择的数据，避免重复添加
          switch (selectModal.type) {
            case 'alert': {
              // 过滤掉已经存在的告警，避免重复添加
              const existingAlertIds = alerts.map(alert => alert.id);
              const newAlerts = selectedItems.filter(item => !existingAlertIds.includes(item.id));
              if (newAlerts.length > 0) {
                setAlerts([...alerts, ...newAlerts]);
              }
              break;
            }
            case 'alertSend': {
              // 过滤掉已经存在的告警发送，避免重复添加
              const existingAlertSendIds = alertSends.map(send => send.id);
              const newAlertSends = selectedItems.filter(item => !existingAlertSendIds.includes(item.id));
              if (newAlertSends.length > 0) {
                setAlertSends([...alertSends, ...newAlertSends]);
              }
              break;
            }
            case 'dbConnection': {
              if (selectedItems.length > 0) {
                setDbConnection(selectedItems[0]);
              }
              break;
            }
            case 'otherInfo': {
              if (selectedItems.length > 0) {
                setOtherInfo(selectedItems[0]);
              }
              break;
            }
          }
          setSelectModal({ visible: false, type: 'alert', multiple: false });
        }}
        onSearch={searchText => {
          // 根据不同类型调用不同的搜索API
          switch (selectModal.type) {
            case 'alert':
              console.log('搜索告警:', searchText);
              // TODO: 实现告警搜索逻辑
              break;
            case 'dbConnection':
              if (searchText) {
                try {
                  const searchFields = JSON.parse(searchText);
                  console.log('搜索数据库连接:', searchFields);
                  // TODO: 实现数据库连接搜索逻辑
                } catch (e) {
                  console.log('重置数据库连接搜索', e);
                  // TODO: 重置搜索结果
                }
              } else {
                console.log('重置数据库连接搜索');
                // TODO: 重置搜索结果
              }
              break;
            case 'alertSend':
              if (searchText) {
                try {
                  const searchFields = JSON.parse(searchText);
                  console.log('搜索告警发送:', searchFields);
                  // TODO: 实现告警发送搜索逻辑
                } catch (e) {
                  console.log('重置告警发送搜索', e);
                  // TODO: 重置搜索结果
                }
              } else {
                console.log('重置告警发送搜索');
                // TODO: 重置搜索结果
              }
              break;
            case 'otherInfo':
              console.log('搜索其他信息:', searchText);
              // TODO: 实现其他信息搜索逻辑
              break;
          }
        }}
      />
    </div>
  );
};

export default ComplexTaskForm;
