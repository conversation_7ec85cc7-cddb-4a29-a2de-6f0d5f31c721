import React, { useState } from 'react';
import { Card, Button, Space, Table, Drawer, App } from 'antd';
import { SendOutlined, CheckOutlined, ClearOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

// 导入重构后的模块
import type { TaskAlert } from '../../types';
import { formStyles } from '../../styles';
import AlertTable from '../AlertTable';

interface AlertConfigTabProps {
  alerts: TaskAlert[];
  onAlertsChange: (alerts: TaskAlert[]) => void;
  onAddAlert: () => void;
  onEditAlert: (index: number) => void;
}

/**
 * 监控项配置标签页组件
 * 管理监控项的配置
 */
const AlertConfigTab: React.FC<AlertConfigTabProps> = ({ alerts, onAlertsChange, onEditAlert }) => {
  const { message } = App.useApp();

  // 抽屉状态管理
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRowsFromChild, setSelectedRowsFromChild] = useState<TaskAlert[]>([]);

  const handleDeleteAlert = (index: number) => {
    const newAlerts = alerts.filter((_, i) => i !== index);
    onAlertsChange(newAlerts);
  };

  // 清空所有选择
  const handleClearAll = () => {
    onAlertsChange([]);
    message.success('已清空所有选择');
  };

  // 处理选择监控项按钮点击
  const handleSelectAlert = () => {
    setDrawerVisible(true);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  // 处理抽屉内的选择确认
  const handleDrawerSelectionConfirm = () => {
    console.log('确认选择时的selectedRows:', selectedRowsFromChild);

    onAlertsChange(selectedRowsFromChild);

    handleDrawerClose();
    message.success(`已选择 ${selectedRowsFromChild.length} 个监控项`);
  };

  // 处理选择变化（从AlertTable传递过来）
  const handleSelectionChange = (selectedAlerts: TaskAlert[]) => {
    console.log('AlertConfigTab 接收到选择变化:', selectedAlerts);
    setSelectedRowsFromChild(selectedAlerts);
  };

  const columns: ColumnsType<TaskAlert> = [
    {
      title: '监控项名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '监控项级别',
      dataIndex: 'severity',
      key: 'severity',
      width: 120,
      render: (severity: string) => (
        <span
          style={{
            color: severity === 'critical' ? '#ff4d4f' : severity === 'high' ? '#fa8c16' : severity === 'medium' ? '#faad14' : '#52c41a',
          }}
        >
          {severity}
        </span>
      ),
    },
    {
      title: '监控项类型',
      dataIndex: 'alert_type',
      key: 'alert_type',
      width: 120,
    },
    {
      title: 'SQL语句',
      dataIndex: 'sql',
      key: 'sql',
      ellipsis: true,
      render: (sql: string) => (
        <code
          style={{
            fontSize: '12px',
            background: '#f5f5f5',
            padding: '2px 4px',
          }}
        >
          {sql}
        </code>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: string, _record: TaskAlert, index: number) => (
        <Space size="small">
          <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEditAlert(index)}>
            查看
          </Button>
          <Button type="text" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDeleteAlert(index)}>
            取消选择
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="关联监控项"
        size="small"
        className="mb-4"
        extra={
          <Space>
            {alerts.length > 0 && (
              <Button danger size="small" type="text" icon={<ClearOutlined />} onClick={handleClearAll}>
                取消全选
              </Button>
            )}
            <Button size="small" type="text" icon={<SendOutlined />} onClick={handleSelectAlert}>
              选择监控项
            </Button>
          </Space>
        }
      >
        <Table dataSource={alerts} rowKey="id" size="small" pagination={false} columns={columns} />
        {alerts.length === 0 && <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无监控项配置，请点击"选择监控项"添加</div>}
      </Card>

      {/* 选择监控项抽屉 */}
      <Drawer
        title={
          <div className="flex justify-between items-center w-full">
            <span>选择监控项配置</span>
            <Button type="primary" icon={<CheckOutlined />} onClick={handleDrawerSelectionConfirm}>
              确认选择 {selectedRowsFromChild.length > 0 && `(${selectedRowsFromChild.length})`}
            </Button>
          </div>
        }
        width="80%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
      >
        <AlertTable contentHeight={800} isSelectionMode={true} selectedRows={alerts} onSelectionConfirm={handleSelectionChange} />
      </Drawer>
    </div>
  );
};

export default AlertConfigTab;
